FROM python:3.9-slim

# Install system dependencies for bluepy
RUN apt-get update && apt-get install -y \
    build-essential \
    libglib2.0-dev \
    bluetooth \
    bluez \
    libcap2-bin \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
#COPY ble_scanner.py .

# Make the script executable
#RUN chmod +x ble_scanner.py

# Create a wrapper script to run with the correct permissions
RUN echo '#!/bin/bash\n\
# Set capabilities for bluepy-helper\n\
if [ -f /usr/local/lib/python3.9/site-packages/bluepy/bluepy-helper ]; then\n\
    setcap "cap_net_raw,cap_net_admin+eip" /usr/local/lib/python3.9/site-packages/bluepy/bluepy-helper\n\
fi\n\
\n\
# Run the BLE scanner\n\
exec python ble_scanner.py\n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Run the application with the wrapper script
CMD ["/app/entrypoint.sh"]
