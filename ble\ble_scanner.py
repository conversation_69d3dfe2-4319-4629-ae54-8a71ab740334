#!/usr/bin/env python3
"""
BLE Scanner - Scans for BLE advertisements and publishes them to MQTT
Designed for Raspberry Pi deployment with support for active scanning
"""
import json
import logging
import os
import signal
import sys
import time
from datetime import datetime
import threading

import paho.mqtt.client as mqtt
from bluepy.btle import Scanner, DefaultDelegate

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ble-scanner")

# MQTT Configuration
MQTT_BROKER = os.environ.get('MQTT_BROKER', 'localhost')
MQTT_PORT = int(os.environ.get('MQTT_PORT', 1883))
MQTT_TOPIC_PREFIX = os.environ.get('MQTT_TOPIC_PREFIX', 'ble/advertisements')
MQTT_CLIENT_ID = os.environ.get('MQTT_CLIENT_ID', 'ble-scanner')

# BLE Scanner Configuration
SCAN_INTERVAL = int(os.environ.get('SCAN_INTERVAL', 3))  # seconds for each scan

# Global shutdown flag
shutdown_flag = threading.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_flag.set()


# Register signal handlers for graceful shutdown
signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)


class ScanDelegate(DefaultDelegate):
    """Delegate class for BLE scanner callbacks"""
    def __init__(self):
        DefaultDelegate.__init__(self)

    def handleDiscovery(self, dev, isNewDev, isNewData):
        """
        Called when a BLE device is discovered

        This is called for each advertisement packet received during scanning.
        For active scanning, this includes both advertisements and scan responses.
        """
        if isNewDev:
            logger.debug(f"Discovered new device: {dev.addr}")
        elif isNewData:
            logger.debug(f"Received new data from: {dev.addr}")


def on_connect(client, userdata, flags, rc):
    """Callback for when the client connects to the MQTT broker"""
    if rc == 0:
        logger.info("Connected to MQTT broker")
    else:
        logger.error(f"Failed to connect to MQTT broker with code: {rc}")


def on_disconnect(client, userdata, rc):
    """Callback for when the client disconnects from the MQTT broker"""
    if rc != 0:
        logger.warning(f"Unexpected disconnection from MQTT broker: {rc}")
    else:
        logger.info("Disconnected from MQTT broker")


def setup_mqtt_client():
    """Set up and return an MQTT client"""
    client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    client.on_connect = on_connect
    client.on_disconnect = on_disconnect

    # Connect to the broker
    try:
        logger.info(f"Connecting to MQTT broker at {MQTT_BROKER}:{MQTT_PORT}")
        client.connect(MQTT_BROKER, MQTT_PORT, 60)
        client.loop_start()
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MQTT broker: {e}")
        # Retry after a delay
        time.sleep(5)
        return setup_mqtt_client()


def scan_ble_devices(timeout=SCAN_INTERVAL):
    """
    Scan for BLE devices and return the results

    Args:
        timeout (float): How long to scan for in seconds

    Returns:
        List of discovered devices
    """
    try:
        # Check if shutdown was requested before starting scan
        if shutdown_flag.is_set():
            return []

        # Create scanner with delegate
        scanner = Scanner().withDelegate(ScanDelegate())

        # Start scanning with shorter intervals to allow for interruption
        # Break the scan into smaller chunks to check shutdown flag
        chunk_size = min(timeout, 0.5)  # Scan in 0.5 second chunks max
        total_time = 0
        all_devices = []

        logger.debug(f"Starting BLE scan for {timeout} seconds")

        while total_time < timeout and not shutdown_flag.is_set():
            remaining_time = timeout - total_time
            scan_time = min(chunk_size, remaining_time)

            devices = scanner.scan(scan_time)
            all_devices.extend(devices)
            total_time += scan_time

            # Small delay between chunks
            if total_time < timeout and not shutdown_flag.is_set():
                time.sleep(0.01)

        return all_devices
    except Exception as e:
        logger.error(f"Error scanning for BLE devices: {e}")
        # If we get a permission error, provide more detailed troubleshooting info
        if "permission" in str(e).lower():
            logger.error("This is likely a permissions issue. Make sure the container has the necessary permissions to access Bluetooth.")
            logger.error("Run with --privileged flag and mount /var/run/bluetooth and /sys/class/bluetooth.")

        return []





def publish_device_data(client, device):
    """Publish device data to MQTT"""
    try:
        # Create a dictionary with device information
        device_data = {
            "address": device.addr,
            "rssi": device.rssi,
            "timestamp": datetime.now().isoformat(),
            "adv_data": {},
            "manufacturer_data": {}  # New field for manufacturer data blocks
        }

        # Try to get the device name
        try:
            device_data["name"] = device.getValueText(9) or "Unknown"  # Complete Local Name
        except Exception as e:
            logger.warning(f"Could not get device name: {e}")
            device_data["name"] = "Unknown"

        # Add advertisement data
        try:
            # Process all advertisement data
            for (adtype, desc, value) in device.getScanData():
                # Store all advertisement data
                device_data["adv_data"][desc] = value

                # Special handling for Manufacturer Data
                if desc == "Manufacturer":
                    # Extract the manufacturer prefix (first 4 characters)
                    if len(value) >= 4:
                        hex_prefix = value[:4]  # First 4 characters (2 bytes in hex)
                        hex_data = value[4:]    # Rest of the data

                        # Convert hex prefix to decimal string (little-endian format)
                        # BLE manufacturer IDs are transmitted in little-endian format
                        # So "0001" means 0x0100 = 256, "0002" means 0x0200 = 512
                        try:
                            # Parse as little-endian 16-bit integer
                            manufacturer_id = int(hex_prefix[2:4] + hex_prefix[0:2], 16)
                            decimal_prefix = str(manufacturer_id)

                            # Convert hex data string to array of bytes
                            data_bytes = []
                            for i in range(0, len(hex_data), 2):
                                if i + 1 < len(hex_data):
                                    byte_val = int(hex_data[i:i+2], 16)
                                    data_bytes.append(byte_val)

                            # Store in manufacturer_data with decimal prefix as key
                            # Use the data_bytes directly (not as an array of arrays)
                            device_data["manufacturer_data"][decimal_prefix] = data_bytes

                            logger.debug(f"Manufacturer data found: Hex={hex_prefix}, Decimal={decimal_prefix}, Data={data_bytes}")
                        except ValueError as e:
                            logger.warning(f"Could not parse manufacturer data: {hex_prefix}, {hex_data}, Error: {e}")
        except Exception as e:
            logger.warning(f"Could not get advertisement data: {e}")

        # Convert to JSON and publish
        topic = f"{MQTT_TOPIC_PREFIX}/{device.addr.replace(':', '')}"
        payload = json.dumps(device_data)
        result = client.publish(topic, payload, qos=0, retain=False)

        if result.rc == mqtt.MQTT_ERR_SUCCESS:
            logger.debug(f"Published data for device {device.addr} to {topic}")
        else:
            logger.warning(f"Failed to publish data for device {device.addr}: {result.rc}")

    except Exception as e:
        logger.error(f"Error publishing device data: {e}")


def main():
    """Main function"""
    logger.info("Starting BLE Scanner")

    # Set up MQTT client
    mqtt_client = setup_mqtt_client()

    try:
        # Start continuous scanning
        logger.info("Starting continuous BLE scanning...")

        while not shutdown_flag.is_set():
            # Scan for devices
            devices = scan_ble_devices(timeout=SCAN_INTERVAL)

            # Log the number of devices found
            logger.debug(f"Found {len(devices)} BLE devices")

            # Publish data for each device
            for device in devices:
                if shutdown_flag.is_set():
                    break
                publish_device_data(mqtt_client, device)

            # Small delay to avoid overwhelming the system, but check shutdown flag
            if not shutdown_flag.is_set():
                time.sleep(0.1)

    except KeyboardInterrupt:
        logger.info("Stopping BLE Scanner (KeyboardInterrupt)")
        shutdown_flag.set()
    except Exception as e:
        logger.error(f"Error in main loop: {e}")
        shutdown_flag.set()
    finally:
        # Clean up
        logger.info("Cleaning up MQTT connection...")
        if mqtt_client:
            try:
                mqtt_client.loop_stop()
                mqtt_client.disconnect()
                # Give a short time for cleanup
                time.sleep(0.5)
            except Exception as e:
                logger.warning(f"Error during MQTT cleanup: {e}")

        logger.info("BLE Scanner stopped")


if __name__ == "__main__":
    main()
